import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button, Checkbox, List, message } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import { PreloadImage } from "@/components/Image";
import folderIcon from "@/Resources/nasDiskImg/file-icon.png";
import { getPoolInfo, listDirectory } from "@/api/fatWall";
import { uploadToBaiduNetdisk, BaiduUploadPathItem } from "@/api/nasDisk";
import BaiduUploadLocationModal from "../BaiduUploadLocationModal";
import { useTaskNotification } from "@/layouts/Layout";

interface UploadModalTwoProps {
  visible: boolean;
  onClose: () => void;
  onUpload: (selectedPaths: string[]) => void;
}

interface FileItem {
  id: string;
  name: string;
  type: "folder" | "file";
  time: string;
  itemCount?: number;
  isLiked?: boolean;
  path: string;
  isDirectory?: boolean;
  dataDir?: string;
}

const UploadModalTwo: React.FC<UploadModalTwoProps> = ({
  visible,
  onClose,
  onUpload,
}) => {
  const { addNotification } = useTaskNotification();
  // 文件列表
  const [fileList, setFileList] = useState<FileItem[]>([]);
  
  // 选中的文件/文件夹（最多5项）
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  
  // 获取存储池信息
  const { run: fetchPoolInfo } = useRequest(getPoolInfo, {
    manual: true,
    onSuccess: (response) => {
      if (response.code === 0 && response.data) {
        // 获取第一个存储池的顶层目录作为顶层文件夹
        if (response.data.internal_pool.length > 0) {
          const firstPool = response.data.internal_pool[0];

          let pathParent = firstPool.data_dir;

          if (response.data.webDAV?.alias_root) {
            const dataDir = firstPool.data_dir.endsWith("/")
              ? firstPool.data_dir.slice(0, -1)
              : firstPool.data_dir;
            const aliasRoot = response.data.webDAV.alias_root;
            pathParent = aliasRoot + dataDir;
          }

          // 获取顶层目录
          fetchDirectoryList({
            path: {
              parent: pathParent,
              recursion: false,
            },
          });
        }
      }
    },
    onError: (error) => {
      console.error("获取存储池信息失败：", error);
      setFileList([]);
    },
  });

  // 获取目录列表
  const { run: fetchDirectoryList } = useRequest(
    listDirectory,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          const files: FileItem[] = response.data.files
            .filter((file) => file.xattr.directory)
            .map((file, index) => ({
              id: `file_${index}`,
              name: file.name,
              type: "folder" as const,
              time: new Date(parseInt(file.modified_time))
                .toLocaleDateString("zh-CN", {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                  hour: "2-digit",
                  minute: "2-digit",
                })
                .replace(/\//g, "/")
                .replace(/,/g, ""),
              itemCount: Math.floor(Math.random() * 50) + 1, // 模拟项目数量
              path: `${file.parent}/${file.name}`,
              isDirectory: file.xattr.directory,
              isLiked: file.xattr.favorite,
            }));
          setFileList(files);
        }
      },
      onError: (error) => {
        console.error("获取目录失败：", error);
        setFileList([]);
      },
    }
  );

  // 初始化时获取存储池信息
  useEffect(() => {
    if (visible) {
      fetchPoolInfo({});
      setSelectedItems([]);
    }
  }, [visible, fetchPoolInfo]);

  // 处理选择变化（限制最多5项）
  const handleSelectionChange = (id: string, checked: boolean) => {
    if (checked) {
      // 如果已经选中了5项，则不能再选中
      if (selectedItems.length >= 5) {
        return;
      }
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(itemId => itemId !== id));
    }
  };

  // 上传位置选择弹窗
  const [locationModalVisible, setLocationModalVisible] = useState<boolean>(false);
  
  // 选择的上传位置
  const [uploadLocation, setUploadLocation] = useState<{
    path: string;
    displayPath: string;
  }>({
    path: '/',
    displayPath: '百度网盘'
  });

  // 打开上传位置选择弹窗
  const handleOpenLocationModal = () => {
    // 延迟打开弹窗，避免ResizeObserver错误
    setTimeout(() => {
      setLocationModalVisible(true);
    }, 50);
  };

  // 处理上传位置选择
  const handleLocationSelect = (path: string, displayPath: string) => {
    setUploadLocation({
      path,
      displayPath
    });
    setLocationModalVisible(false);
  };

  // 重置弹窗状态
  const resetModalState = () => {
    setSelectedItems([]);
    setLocationModalVisible(false);
  };

  // 弹窗关闭时重置状态
  const handleModalClose = () => {
    resetModalState();
    onClose();
  };

  // 使用useRequest处理上传
  const { run: runUpload, loading: uploadLoading } = useRequest(
    uploadToBaiduNetdisk,
    {
      manual: true,
             onSuccess: (response, params) => {
         if (response.code === 0) {
           message.success(`上传任务已创建，任务ID: ${response.task_id || '未知'}`);
           // 调用成功回调（传递上传的路径信息）
           const paths = params[0].localpath.map((item: BaiduUploadPathItem) => item.path);
           onUpload(paths);
           // 重置选择状态
           setSelectedItems([]);
           onClose();
           // 添加任务通知
           addNotification();
         } else {
          const errorMessages: Record<number, string> = {
            2: "参数错误",
            9: "文件或目录不存在",
            111: "有其他异步任务正在执行",
            112: "页面已过期，请刷新后重试",
            117: "网络连接问题，请稍后重试",
          };
          
          const errorMessage = errorMessages[response.errno || response.code] || response.errmsg || "上传失败";
          message.error(errorMessage);
        }
      },
      onError: (error) => {
        console.error("上传请求失败:", error);
        message.error("上传失败，请检查网络连接");
      },
    }
  );

  // 处理上传
  const handleUpload = () => {
    if (selectedItems.length === 0) {
      message.warning("请选择要上传的文件夹");
      return;
    }

    const selectedPaths = fileList
      .filter(file => selectedItems.includes(file.id))
      .map(file => file.path);

    // 格式化为对象数组格式
    const formattedLocalpath: BaiduUploadPathItem[] = selectedPaths.map(path => ({
      type: "directory",
      path: path
    }));

    console.log("准备上传到百度网盘:", {
      remotepath: uploadLocation.path,
      localpath: formattedLocalpath,
    });

    // 调用百度网盘上传接口
    runUpload({
      action: "upload",
      autotask: 1,
      remotepath: uploadLocation.path,
      localpath: formattedLocalpath,
    });
  };

  return (
    <Modal
      title={
        <div className={styles.modalHeader}>
          <ArrowLeftOutlined 
            className={styles.backIcon} 
            onClick={uploadLoading ? undefined : handleModalClose}
            style={{ 
              cursor: uploadLoading ? 'not-allowed' : 'pointer',
              color: uploadLoading ? 'rgba(0, 0, 0, 0.25)' : undefined
            }}
          />
          <span className={styles.modalTitle}>选择文件夹（最多5项）</span>
        </div>
      }
      open={visible}
      footer={null}
      onCancel={uploadLoading ? undefined : handleModalClose}
      width={546}
      className={styles.uploadModal}
      closeIcon={null}
      centered
      maskClosable={!uploadLoading}
      styles={{
        body: {
          height: "calc(636px - 90px)",
          padding: 0,
          overflow: "hidden",
        },
      }}
    >
      <div className={styles.modalContent}>
        <div className={styles.breadcrumbHeader}>
          <div className={styles.breadcrumbContainer}>
            <span className={`${styles.breadcrumbItem} ${styles.breadcrumbCurrent}`}>
              内部存储
            </span>
          </div>
        </div>
        
        <div className={styles.fileListContainer}>
          {fileList.length > 0 ? (
            <List
              className={styles.fileList}
              dataSource={fileList}
              renderItem={(file) => (
                <List.Item 
                  key={file.id}
                  className={`${styles.fileItem} ${selectedItems.includes(file.id) ? styles.selectedItem : ''}`}
                >

                  <div 
                    className={`${styles.fileContent} ${selectedItems.includes(file.id) ? styles.selectedContent : ''}`}
                    onClick={() => handleSelectionChange(file.id, !selectedItems.includes(file.id))}
                    title="点击选择文件夹"
                  >
                    <PreloadImage 
                      src={folderIcon} 
                      alt="folder" 
                      className={styles.fileIcon} 
                    />
                    <div className={styles.fileInfo}>
                      <div className={styles.fileName}>{file.name}</div>
                      <div className={styles.fileDetails}>
                        {file.time} | {file.itemCount}项
                      </div>
                    </div>
                  </div>
                                    <div className={styles.checkboxCell}>
                    <Checkbox 
                      checked={selectedItems.includes(file.id)}
                      onChange={(e) => handleSelectionChange(file.id, e.target.checked)}
                      onClick={(e) => e.stopPropagation()}
                      className={styles.checkbox}
                      disabled={!selectedItems.includes(file.id) && selectedItems.length >= 5}
                    />
                  </div>
                </List.Item>
              )}
            />
          ) : (
            <div className={styles.emptyContainer}>
              <div className={styles.emptyText}>该目录下没有文件夹</div>
            </div>
          )}
        </div>
        
        <div className={styles.footerContainer}>
          <div className={styles.uploadPathContainer}>
            <Button 
              className={styles.uploadPathButton}
              onClick={handleOpenLocationModal}
            >
              自动上传到: <span className={styles.uploadPath}>{uploadLocation.displayPath}</span>
            </Button>
          </div>
          <Button  
            className={styles.uploadButton}
            disabled={selectedItems.length === 0 || uploadLoading}
            loading={uploadLoading}
            onClick={handleUpload}
          >
            {uploadLoading ? '上传中...' : `上传 (${selectedItems.length})`}
          </Button>
        </div>
      </div>
      
      {/* 上传位置选择弹窗 */}
      <BaiduUploadLocationModal
        visible={locationModalVisible}
        onClose={() => setLocationModalVisible(false)}
        onSelect={handleLocationSelect}
      />
    </Modal>
  );
};

export default UploadModalTwo; 