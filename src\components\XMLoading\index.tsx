import styles from './index.module.scss';
import loading_img from '@/Resources/filmWall/loading.png';
import loading_img_dark from '@/Resources/filmWall/loading_dark.png';
import { useTheme } from '@/utils/themeDetector';
import { PreloadImage } from '../Image';
import { useMemo } from 'react';
import { px2rem } from '@/utils/setRootFontSize';

type size = 'small' | 'medium' | 'large';

type sizeNumber = number;

interface LoadingProps {
  loading: boolean;
  size: size | sizeNumber;
}

const XMLoading = (props: LoadingProps) => {
  const { isDarkMode } = useTheme();
  const { loading, size } = props;

  const loadingSize = useMemo(() => {
    if (typeof size === 'number') {
      return px2rem(`${size}px`);
    }

    switch (size) {
      case 'small':
        return px2rem('12px');
      case 'medium':
        return px2rem('24px');
      case 'large':
        return px2rem('36px');
      default:
        return px2rem('12px');
    }
  }, [size])

  return loading ? <div style={{ "--loading-size": loadingSize } as React.CSSProperties} className={styles.loading_container}>
    <PreloadImage src={isDarkMode ? loading_img_dark : loading_img} alt='loading' />
  </div> : <></>
}

export default XMLoading;