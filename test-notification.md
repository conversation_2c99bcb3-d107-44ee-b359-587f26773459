# 任务通知功能测试说明

## 功能概述
实现了一个任务通知系统，当上传至网盘或下载至存储的任务创建成功时，会在Layout.tsx的侧边导航栏的"任务管理"菜单项上显示红点通知。

## 实现的功能

### 1. 通知Context (Layout.tsx)
- 创建了 `TaskNotificationContext` 来管理通知状态
- 提供 `addNotification()` 方法增加通知计数
- 提供 `clearNotifications()` 方法清除通知
- 在任务管理菜单项上显示红点通知

### 2. 红点样式 (Layout.module.scss)
- 添加了 `.notification_dot` 样式
- 红色圆点，位于菜单文字右上角
- 只有当 `notificationCount > 0` 且菜单key为 `taskManager` 时显示

### 3. 集成的组件
以下组件在任务创建成功后会触发通知：

#### PC端组件：
- `BDfiles/index.tsx` - 主要的网盘文件页面
- `UploadModal/index.tsx` - 上传文件弹窗
- `UploadModalTwo/index.tsx` - 上传文件弹窗2
- `DownloadModal/index.tsx` - 下载文件弹窗

#### APP端组件：
- `FileUpload/index.tsx` - 文件上传页面
- `Synchronization/index.tsx` - 同步上传页面
- `SelectFolder/index.tsx` - 选择文件夹下载页面
- `FileList/index.tsx` - 文件列表组件

## 触发条件
- 上传任务创建成功 (response.code === 0)
- 下载任务创建成功 (result.code === 0)

## 清除条件
- 点击"任务管理"菜单项时自动清除通知红点

## 测试步骤
1. 在网盘文件页面选择文件进行上传或下载
2. 任务创建成功后，观察侧边栏"任务管理"菜单项是否出现红点
3. 点击"任务管理"菜单项，红点应该消失
4. 重复上述步骤验证功能正常

## 注意事项
- 通知计数会累加，每次成功创建任务都会增加计数
- 只有点击任务管理菜单才会清除通知
- 红点样式适配了明暗主题
