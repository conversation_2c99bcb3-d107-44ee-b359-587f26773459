import { Route, useLocation } from "react-router-dom";
import { useRequest } from "ahooks";
import IPCHome from "./Home";
import NotAdded from "./NotAdded";
import styles from "./index.module.scss";
import { useState, useEffect } from "react";
import { listRecordCamera, CameraInfo } from "@/api/ipc";
import { Toast } from "antd-mobile";
import loadingIcon from "@/Resources/camMgmtImg/loading.png";

export default function CameraManagement(props: {
  children?: React.ReactNode;
}) {
  const [hasCameras, setHasCameras] = useState(false);
  const [cameraList, setCameraList] = useState<CameraInfo[]>([]);
  // const { path } = useRouteMatch();
  const location = useLocation<{ shouldRefresh?: boolean }>();

  const { run: fetchCameraList,loading } = useRequest(
    () => listRecordCamera({ did: [] }, { showLoading: false }),
    {
      manual: false,
      onSuccess: (res) => {
        if(res && (res.code !== 0)){
          Toast.show(res?.result);
          return;
        }
        const cameras = res.data?.camera || [];
        setCameraList(cameras);
        setHasCameras(cameras.length > 0);
      },
      onError: (err) => {
        console.error("摄像头列表获取失败:", err);
        setCameraList([]);
        setHasCameras(false);
      },
    }
  );

  // 提供刷新摄像头列表的方法
  const refreshCameraList = () => {
    fetchCameraList();
  };

  // 监听路由状态变化，如果需要刷新则重新获取数据
  useEffect(() => {
    if (location.state?.shouldRefresh) {
      fetchCameraList();
    }
  }, [location.state?.shouldRefresh, fetchCameraList]);

  if (loading)
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingContent}>
          <img src={loadingIcon} alt="loading" className={styles.loadingIcon} />
          <span className={styles.loadingText}>加载中</span>
        </div>
      </div>
    );

  return (
    <div id="cameraManagementContainer" className={styles.cameraManagementContainer}>
      <div className={styles.top}></div>
      <div className={styles.content}>
        {props.children}
        <Route exact path="/cameraManagement_app">
          {hasCameras ? (
            <IPCHome
              cameraList={cameraList}
              refreshCameraList={refreshCameraList}
            />
          ) : (
            <NotAdded />
          )}
        </Route>
      </div>
    </div>
  );
}
