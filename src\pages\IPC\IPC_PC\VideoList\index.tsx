import React, {
  useState,
  useMemo,
  useCallback,
  useRef,
  useEffect,
} from "react";
import { PreloadImage } from "@/components/Image";
import styles from "./index.module.scss";
import dataSelect from "@/Resources/player/dateSelect.png";
import refreshBtn from "@/Resources/layout/refreshBtn.png";
import refreshDarkBtn from "@/Resources/layout/refreshBtn_dark.png";
import selected from "@/Resources/icon/selected.png";
import notSelect from "@/Resources/icon/not_select.png";
import nullData from "@/Resources/camMgmtImg/null-page.png";
import nullDataDark from "@/Resources/camMgmtImg/null-page-dark.png";
import { DatePicker, ConfigProvider } from "antd";
import locale from "antd/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import type { Dayjs } from "dayjs";
import PopoverSelector from "@/components/PopoverSelector";
import {
  eventDefinition,
} from "@/components/CameraPlayer/constants";
import { ICameraDetail } from "../../IPC_APP/CameraDetail";
import { ICollapsePanel } from "@/layouts/Layout";
import { getVideoRecord, delRecordVideo } from "@/api/ipc";
import { useCameras } from "..";
import { splitURL } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";
import { format } from "date-fns";
import { downloadFiles } from "@/api/fatWallJSBridge";
import { Toast } from "@/components/Toast/manager";
import { modalShow } from "@/components/List";
import { eventLookBackData, eventLookBackDataType } from "../EventLookBack";
import LookBackMovieList from "../EventLookBack/LookBackMovieList";
import outlineIcon from "@/Resources/camMgmtImg/outline.png";
import outlineDarkIcon from "@/Resources/camMgmtImg/outline-dark.png";
import { useTheme } from "@/utils/themeDetector";
import downloadIcon from "@/Resources/icon/download.png";
import downloadWhiteIcon from "@/Resources/icon/download_white.png";
import deleteIcon from "@/Resources/icon/delete.png";
import deleteWhiteIcon from "@/Resources/icon/delete_white.png";
import event_error_img_dark from "@/Resources/camera_poster/camera_manager_eventLookback_poster_dark.png";
import event_error_img from "@/Resources/camera_poster/camera_manager_eventLookback_poster.png";

dayjs.locale("zh-cn");

// 视频项接口
interface VideoItem {
  camera_lens: string;
  event_name: string;
  time: string;
  media_duration: number;
  file: string;
  create_time: string;
  cover_file: string;
  face_info: {
    uuid: string;
    name: string;
    profile_pic: string;
  }[];
  timeLabel?: string;
  thumbnail?: string;
}

export default function VideoList() {
  const { cameras } = useCameras();
  const { isDarkMode } = useTheme();

  const [deviceDetail, setDeviceDetail] = useState<
  (ICollapsePanel & ICameraDetail)[]
  >([]);
  const [deviceIsShowSelector, setDeviceIsShowSelector] =
    useState<boolean>(false);
  const [curDeviceKey, setCurDeviceKey] = useState<string>("all");
  const [videoData, setVideoData] = useState<VideoItem[]>([]);
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(dayjs()); // 初始值为今天
  const [hoverKey, setHoverKey] = useState<string>(""); // 悬浮key
  const [selectList, setSelectList] = useState<VideoItem[]>([]); // 已选择list
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const pageRef = useRef<{ size: number; token: string }>({
    size: 20,
    token: "",
  });
  const [curData, setCurData] = useState<eventLookBackDataType | undefined>();
  const [modalIsShow, setModalIsShow] = useState<boolean>(false);
  const [eventData, setEventData] = useState<eventLookBackData[]>([]);

  // 设置设备详情
  useEffect(() => {
    if (cameras && cameras.length > 0) {
      setDeviceDetail(
        cameras.map((it) => ({
          ...it,
          label: it.model,
          key: it.did,
          name: it.name,
          // icon: it?.icon,
        }))
      );
    }
  }, [cameras]);

  // 当cameras数据可用时，默认选择第一个摄像头
  useEffect(() => {
    if (cameras && cameras.length > 0 && curDeviceKey === "all") {
      setCurDeviceKey(cameras[0].did);
    }
  }, [cameras, curDeviceKey]);

  // 页面曝光埋点
  useEffect(() => {
    window.onetrack?.('track', 'ipc_videoList_expose');
  }, []);

  // 当前设备
  const curDevice: (ICollapsePanel & ICameraDetail) | undefined =
    useMemo(() => {
      return deviceDetail.find((item) => item.key === curDeviceKey);
    }, [deviceDetail, curDeviceKey]);

  // 生成camera_lens参数
  const camera_lens = useMemo(() => {
    if (!deviceDetail.length) return [];
    const temp: string[] = [];
    if (curDeviceKey === "all") {
      deviceDetail.forEach((it) => {
        it.key_frame.forEach((item) => temp.push(`${it.did}_${item.lens_id}`));
      });
    } else if (curDevice) {
      curDevice.key_frame.forEach((it) => {
        temp.push(`${curDevice.did}_${it.lens_id}`);
      });
    }
    return temp;
  }, [curDevice, curDeviceKey, deviceDetail]);

  // 设备选择选项
  const deviceSelectOptions = useMemo(() => {
    const ops = deviceDetail.map((item) => {
      return {
        label: item.name,
        value: item.key,
        // icon: item.icon,
        subtitle: item.model,
      };
    });
    // ops.unshift({ label: '全部设备', value: 'all', icon: '', subtitle: '' });
    return ops;
  }, [deviceDetail]);

  // 获取视频数据
  const getData = useCallback(async () => {
    if (camera_lens.length === 0) return;

    setLoading(true);

    let params;

    if (selectedDate) {
      // 如果选择了日期，获取指定日期的视频数据
      const startOfDay = selectedDate.startOf("day");
      const endOfDay = selectedDate.endOf("day");

      params = {
        page: pageRef.current,
        options: {
          option: ["camera_lens", "time"],
          camera_lens: camera_lens,
          time: {
            start: Math.floor(startOfDay.valueOf()).toString(),
            end: Math.floor(endOfDay.valueOf()).toString(),
          },
        },
      };
    } else {
      // 如果没有选择日期，获取所有视频数据（不传时间参数）
      params = {
        page: pageRef.current,
        options: {
          option: ["camera_lens"],
          camera_lens: camera_lens,
        },
      };
    }

    try {
      const res = await getVideoRecord(params);
      if (res && res.code === 0 && res.data) {
        let obj: { [key: string]: eventLookBackDataType[] } = {};
        const videos = res.data.videos.map((video: any) => ({
          ...video,
          timeLabel: generateTimeRangeLabel(video.time, video.media_duration),
          thumbnail: video.cover_file
            ? `${video.cover_file}/original.jpg`
            : undefined,
        }));

        // 处理分页逻辑
        if (pageRef.current.token === "") {
          // 首次加载或重置，直接设置数据
          setVideoData(videos);
        } else {
          // 加载更多，追加数据
          setVideoData((prevData) => [...prevData, ...videos]);
        }

        // 更新分页token
        if (res.data.page && res.data.page.token) {
          pageRef.current = { ...pageRef.current, token: res.data.page.token };
        }

        // 检查是否还有更多数据
        if (videos.length < pageRef.current.size) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }

        setEventData(
          Object.keys(obj).map((key) => {
            return { date: key, data: obj[key] };
          })
        );
      }

      if (res && res.code === 5000) {
        Toast.show('请求超时，请稍后再试');
      }
    } catch (error) {
      console.error("获取视频数据失败:", error);
      Toast.show('请求超时，请稍后再试');
    } finally {
      setLoading(false);
    }
  }, [camera_lens, selectedDate]);

  // 生成时间范围标签
  const generateTimeRangeLabel = (
    startTimestamp: string | number,
    durationSeconds: number
  ): string => {
    try {
      const startTime =
        typeof startTimestamp === "string"
          ? parseInt(startTimestamp)
          : startTimestamp;
      const startDate = new Date(startTime);
      const endDate = new Date(startTime + durationSeconds);

      const startTimeStr = format(startDate, "HH:mm");
      const endTimeStr = format(endDate, "HH:mm");

      return `${startTimeStr}-${endTimeStr}`;
    } catch (error) {
      console.error("时间范围标签生成失败:", error);
      return "00:00-00:00";
    }
  };

  // 当设备或日期改变时重新获取数据
  useEffect(() => {
    getData();
  }, [getData]);

  // 加载更多数据
  const loadMoreData = useCallback(() => {
    if (!loading && hasMore) {
      getData();
    }
  }, [loading, hasMore, getData]);

  // 处理日期选择
  const handleDateChange = (date: Dayjs | null) => {
    setSelectedDate(date);
    pageRef.current = { size: 20, token: "" }; // 重置分页信息
    setHasMore(true); // 重置hasMore状态
  };

  // 选中回调
  const clickCallback = useCallback((video: VideoItem) => {
    setSelectList((prev: VideoItem[]) => {
      let newList = [...prev];
      const isIncludes = newList.some(
        (item) =>
          item.camera_lens === video.camera_lens && item.time === video.time
      );
      if (isIncludes) {
        newList = newList.filter(
          (item) =>
            !(
              item.camera_lens === video.camera_lens && item.time === video.time
            )
        );
      } else {
        newList.push(video);
      }
      return newList;
    });
  }, []);

  // 处理全选/取消全选
  const handleSelectAll = () => {
    if (selectList.length === videoData.length) {
      setSelectList([]);
    } else {
      setSelectList([...videoData]);
    }
  };

  // 清空选择
  const clearSelection = () => {
    setSelectList([]);
    setHoverKey("");
  };

  // 下载选中的视频
  const handleDownload = () => {
    if (selectList.length === 0) {
      Toast.show("请选择要下载的视频");
      return;
    }

    // 构建文件信息数组
    const fileList = selectList.map((video) => ({
      name: `${video.camera_lens}_${video.time}.mp4`, // 生成文件名
      path: video.file,
    }));

    downloadFiles(fileList, (res) => {
      if (res && res.code === 0) {
        Toast.show("正在下载");
        // 下载成功后清空选择
        clearSelection();
      } else {
        Toast.show("下载失败，请稍后再试");
      }
    });
  };

  // 删除选中的视频
  const handleDelete = () => {
    if (selectList.length === 0) {
      Toast.show("请选择要删除的视频");
      return;
    }

    // 显示删除确认弹窗
    modalShow(
      "是否确定删除?",
      <div
        style={{
          textAlign: "center",
          color: "var(--text-color)",
          fontSize: "16px",
          lineHeight: "24px",
          padding: "8px 0",
        }}
      >
        文件删除后将无法恢复
      </div>,
      async (m) => {
        try {
          // 构建文件路径数组
          const filePaths = selectList.map((video) => video.file);

          // 调用删除接口
          const response = await delRecordVideo({ videos: filePaths });

          // 检查响应状态
          if (response.code === 0) {
            Toast.show("删除成功");

            // 删除成功后重新获取视频数据
            pageRef.current = { size: 20, token: "" }; // 重置分页信息
            setHasMore(true); // 重置hasMore状态
            getData();

            // 清空选择并关闭弹窗
            clearSelection();
          } else {
            Toast.show("删除失败，请重试");
          }

          m.destroy();
        } catch (error) {
          console.error("删除失败:", error);
          Toast.show("删除失败，请重试");
          m.destroy();
        }
      },
      () => { },
      false,
      {
        position: "center",
        okBtnText: "确定",
        cancelBtnText: "取消",
        okBtnStyle: {
          backgroundColor: "var(--cancel-btn-background-color)",
          color: "red",
        },
      }
    );
  };

  // 刷新数据
  const handleRefresh = () => {
    clearSelection(); // 清空选择
    pageRef.current = { size: 20, token: "" }; // 重置分页信息
    setHasMore(true); // 重置hasMore状态
    getData();
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <div className={styles.emptyState}>
      <div className={styles.emptyIcon}>
        <PreloadImage
          src={isDarkMode ? nullDataDark : nullData}
          alt="暂无数据"
        />
      </div>
      <div className={styles.emptyText}>暂无内容</div>
    </div>
  );

  // 格式化日期显示
  const dateDisplayText = useMemo(() => {
    if (!selectedDate) {
      return "选择日期"; // 未选择日期时的默认显示
    }

    const today = dayjs();
    const yesterday = today.subtract(1, 'day');

    // 判断是否为今天
    if (selectedDate.isSame(today, "day")) {
      return "今天";
    }

    // 判断是否为昨天
    if (selectedDate.isSame(yesterday, "day")) {
      return "昨天";
    }

    // 判断是否为今年
    if (selectedDate.isSame(today, "year")) {
      return selectedDate.format("M月DD日");
    }

    // 其他年份显示年月日
    return selectedDate.format("YYYY年M月DD日");
  }, [selectedDate]);

  const lookBackDetail = useCallback((video) => {
    const date = new Date(Number(video?.time));
    const time: string = format(date, "HH:mm");
    setCurData({
      id: video?.id,
      eventTime: time,
      eventName: eventDefinition[video?.event_name]?.label,
      deviceName: "",
      url: video?.cover_file || "",
      movieUrl: video?.file,
    });
    setModalIsShow(true);
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.selectedInfoRow}>
          {selectList.length > 0 && (
            <div className={styles.selectedInfo}>
              <span>已选择{selectList.length}项</span>
            </div>
          )}
        </div>

        {/* 操作按钮行 - 左侧操作按钮与右侧按钮水平对齐 */}
        <div className={styles.operationRow}>
          {/* 左侧：选中状态下的操作按钮 */}
          <div className={styles.leftActions}>
            {selectList.length > 0 && (
              <>
                <div className={styles.operationItem} onClick={handleDownload}>
                  <span>下载到本机</span>
                </div>

                <div className={styles.operationItem} onClick={handleDelete}>
                  <span style={{ color: "var(--emergency-text-color" }}>删除</span>
                </div>
              </>
            )}
          </div>

          {/* 右侧：始终显示的操作按钮 */}
          <div className={styles.rightActions}>
            {/* 设备选择器 */}
            <PopoverSelector
              visible={deviceIsShowSelector}
              onVisibleChange={setDeviceIsShowSelector}
              onChange={(value) => {
                setCurDeviceKey(value);
                pageRef.current = { size: 20, token: "" }; // 重置分页信息
              }}
              options={deviceSelectOptions}
              value={curDevice ? curDevice.key : "all"}
            >
              <div className={styles.operationItem}>
                <span className={styles.cameraList}>
                  {curDevice
                    ? curDevice.name
                    : cameras && cameras.length > 0
                      ? cameras[0].name
                      : "全部设备"}
                </span>
                <PreloadImage src={dataSelect} alt="select" />
              </div>
            </PopoverSelector>

            {/* 日期选择器 */}
            <div className={styles.operationItem}>
              <ConfigProvider locale={locale}>
                <DatePicker
                className={styles.operationItem_datePicker}
                  placeholder={dateDisplayText}
                  value={selectedDate}
                  onChange={handleDateChange}
                  disabledDate={(current) =>
                    current && current > dayjs().endOf("day")
                  }
                  allowClear
                //   suffixIcon={<PreloadImage src={dataSelect} alt='select' />}
                />
              </ConfigProvider>
            </div>

            {/* 全选按钮 */}
            <div className={styles.operationItem} onClick={handleSelectAll}>
              <PreloadImage src={isDarkMode ? outlineDarkIcon : outlineIcon} alt="select-all" />
              {/* <span>
                {selectList.length === videoData.length && videoData.length > 0
                  ? "取消全选"
                  : "全选"}
              </span> */}
            </div>

            {/* 刷新按钮 */}
            <div className={styles.operationItem} onClick={handleRefresh}>
              <PreloadImage src={isDarkMode ? refreshDarkBtn : refreshBtn} alt="refresh" />
              {/* <span>刷新</span> */}
            </div>
          </div>
        </div>
      </div>

      <div className={styles.content}>
        {/* 日期标题 */}
        {selectedDate && (
          <div className={styles.dateTitle}>
            {selectedDate.format("YYYY年M月DD日")} |{" "}
            {selectedDate.isSame(dayjs(), "day")
              ? "今天"
              : selectedDate.format("dddd")}
          </div>
        )}

        {loading ? (
          <div className={styles.loading}>
            <span className={styles.loadingText}>加载中...</span>
          </div>
        ) : videoData.length === 0 ? (
          renderEmptyState()
        ) : (
          <>
            <div className={styles.videoGrid}>
              {videoData.map((video, index) => {
              const videoKey = `${video.camera_lens}-${video.time}-${index}`;
              const isSelected = selectList.some(
                (item) =>
                  item.camera_lens === video.camera_lens &&
                  item.time === video.time
              );

              return (
                <div
                  key={videoKey}
                  className={styles.videoItem}
                  //   style={{ background: isSelected ? 'var(--fat-card-hover-bg)' : '' }}
                  onMouseLeave={() => setHoverKey("")}
                  onMouseEnter={() => setHoverKey(videoKey)}
                  onClick={() => {
                    lookBackDetail(video);
                  }}
                >
                  <div className={styles.thumbnailContainer}>
                    {video.thumbnail ? (
                      <PreloadImage
                        src={splitURL(video.thumbnail)}
                        alt=""
                        className={styles.thumbnailImage}
                        needHeader={true}
                        errorImage={isDarkMode ? event_error_img_dark : event_error_img}
                      />
                    ) : (
                      <div className={styles.thumbnailPlaceholder}>
                        <span>暂无缩略图</span>
                      </div>
                    )}

                    <div
                      className={styles.selectItem}
                      onClick={(e) => {
                        e.stopPropagation();
                        clickCallback(video);
                      }}
                      style={{
                        visibility:
                          isSelected || hoverKey === videoKey
                            ? "visible"
                            : "hidden",
                      }}
                    >
                      {isSelected ? (
                        <PreloadImage src={selected} alt="selected" />
                      ) : (
                        <PreloadImage src={notSelect} alt="notSelect" />
                      )}
                    </div>
                  </div>
                  <div className={styles.timeLabel}>
                    {video.timeLabel ||
                      format(new Date(parseInt(video.time) * 1000), "HH:mm")}
                  </div>
                </div>
              );
              })}
            </div>

            {/* 加载更多按钮 */}
            {hasMore && (
              <div className={styles.loadMoreContainer}>
                <button
                  className={styles.loadMoreButton}
                  onClick={loadMoreData}
                  disabled={loading}
                >
                  {loading ? "加载中..." : "加载更多"}
                </button>
              </div>
            )}

            {/* 没有更多数据提示 */}
            {!hasMore && (
              <div className={styles.noMoreData}>
                没有更多数据了
              </div>
            )}
          </>
        )}
      </div>
      <LookBackMovieList
        data={eventData}
        curData={curData}
        isShow={modalIsShow}
        setIsShow={setModalIsShow}
        customOperations={[
          {
            label: '保存到本机',
            name: 'saveToLocal',
            navBarIcon: downloadWhiteIcon,
            icon: isDarkMode ? downloadWhiteIcon : downloadIcon,
            // color:'var(--text-color)',
            onClick: (data) => {
              if (data) {
                // 构建文件信息数组，调用handleDownload的逻辑
                const fileList = [{
                  name: `${data.deviceName || 'camera'}_${data.eventTime}.mp4`,
                  path: data.movieUrl,
                }];

                downloadFiles(fileList, (res) => {
                  if (res && res.code === 0) {
                    Toast.show("正在下载");
                  } else {
                    Toast.show("下载失败，请稍后再试");
                  }
                });
              }
            }
          },
          {
            label: '删除',
            name: 'deleteVideo',
            navBarIcon: deleteWhiteIcon,
            icon: isDarkMode ? deleteWhiteIcon : deleteIcon,
            // color:'var(--text-color)',
            onClick: (data) => {
              if (data) {
                // 显示删除确认弹窗，调用handleDelete的逻辑
                modalShow(
                  "是否确定删除?",
                  <div
                    style={{
                      textAlign: "center",
                      color: "var(--text-color)",
                      fontSize: "16px",
                      lineHeight: "24px",
                      padding: "8px 0",
                    }}
                  >
                    文件删除后将无法恢复
                  </div>,
                  async (m) => {
                    try {
                      // 调用删除接口
                      const response = await delRecordVideo({ videos: [data.movieUrl] });

                      // 检查响应状态
                      if (response.code === 0) {
                        Toast.show("删除成功");

                        // 删除成功后重新获取视频数据
                        pageRef.current = { size: 20, token: "" }; // 重置分页信息
                        getData();

                        // 关闭弹窗
                        setModalIsShow(false);
                      } else {
                        Toast.show("删除失败，请重试");
                      }

                      m.destroy();
                    } catch (error) {
                      console.error("删除失败:", error);
                      Toast.show("删除失败，请重试");
                      m.destroy();
                    }
                  },
                  () => { },
                  false,
                  {
                    position: "center",
                    okBtnText: "确定",
                    cancelBtnText: "取消",
                    okBtnStyle: {
                      backgroundColor: "var(--cancel-btn-background-color)",
                      color: "red",
                    },
                  }
                );
              }
            }
          }
        ]}
      />
    </div>
  );
}
