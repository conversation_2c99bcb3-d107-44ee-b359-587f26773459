.cameraManagementContainer {
  overflow: auto;
  width: 100%;
  height: 100vh;
  margin: 0;
  // overflow-y: auto; /* 启用滚动 */
  // scrollbar-width: none; /* Firefox */
  // -ms-overflow-style: none; /* IE/Edge */

  // padding-top: 35px; // 给手机顶部留出空间
  background: var(--background-color); // 留出顶部后设置空白区背景色

  :global {
    .adm-image {
      margin-top: 0;
    }
  }
}

.top {
  width: 100%;
  height: 35px;
}

.content {
  width: 100%;
  height: calc(100% - 35px);
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.3);
}

.loadingContent {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 24px;
  border-radius: 24px;
  min-width: calc(100% - 56px);
  margin-bottom: 24px;
}

.loadingIcon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

.loadingText {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.8);
  font-family: MiSans W;
  font-weight: 500;
  font-style: Medium;
  line-height: 150%;
  letter-spacing: 0px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
