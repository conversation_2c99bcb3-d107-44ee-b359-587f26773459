import axios from 'axios'
import { Toast } from 'antd-mobile'
import { DotLoading, SpinLoading } from 'antd-mobile'
import React from 'react';
import ReactDOM from 'react-dom';
import './assets/index.css';
import { getDeviceInfo, fetchDeviceInfo, getDeviceType } from "./utils/DeviceType";

let deviceInfo = null;
// 默认域名
//获取当前域名
const initialize = async (type) => {
  const hostWithoutPort = window.location.host.replace(/:\d+$/, '');
  try {
    await fetchDeviceInfo(type); // 获取设备信息
    deviceInfo = getDeviceInfo();
    const cgiPort = deviceInfo.cgiPort;
    if (!deviceInfo || !deviceInfo.cgiPort) {
      throw new Error('设备信息或 cgiPort 不存在');
    }
    // 配置 axios 的 baseURL
    axios.defaults.baseURL = `https://${hostWithoutPort}:${cgiPort}/cgi-bin/luci/`;
    console.log('Axios baseURL:', axios.defaults.baseURL);
    // alert(`初始化成功!${axios.defaults.baseURL}`);
  } catch (error) {
    axios.defaults.baseURL = `https://${hostWithoutPort}:443/cgi-bin/luci/`; // 设置默认的打包 baseURL
    console.log('Axios初始化失败:', error.message);
    // alert(`初始化失败!${axios.defaults.baseURL}`);
  }
};
await initialize('');
axios.defaults.baseURL = `https://*************:443/cgi-bin/luci/`; // 设置默认的打包 baseURL
console.log(axios.defaults.baseURL)
// 配置请求头
axios.defaults.headers["Content-Type"] = "application/json";
// 响应时间
axios.defaults.timeout = 10000;
//请求拦截器
axios.interceptors.request.use(
  config => {
    // 检查是否需要显示loading，默认显示
    if (config.showLoading !== false) {
      // 检查loading模式，默认为full（全屏遮罩+图标），可选icon（仅图标）
      const loadingMode = config.loadingMode || 'full';
      showLoading(loadingMode);//显示加载动画
    }
    if (deviceInfo && deviceInfo.cgiToken) {
      // 设置统一的请求header
      config.headers.authorization = deviceInfo.cgiToken //授权(每次请求把token带给后台)
    } else {
      config.headers.authorization = 'tkH4UgCulcypg9ZlczqhD1RI5VNE1me6';
    }
    return config;
  },
  error => {
    hideLoading();//关闭加载动画
    console.log('错误信息:', error && JSON.stringify(error))
    return Promise.reject(error);
  }
);

//响应拦截器
axios.interceptors.response.use(
  response => {
    // 检查是否需要隐藏loading，默认隐藏
    if (response.config.showLoading !== false) {
      hideLoading();//关闭加载动画
    }
    return response;
  },
  error => {
    hideLoading();//关闭加载动画
    return Promise.resolve(error.response);
  }
);

// 处理请求返回的数据
function checkStatus(response, originalRequest) {
  return new Promise((resolve, reject) => {
    if (!response) {
      console.log('响应失败，尝试重新请求...', originalRequest && JSON.stringify(originalRequest.url));
      // 重新初始化并重试当前请求
      initialize("refresh").then(() => {
        // 重新请求原接口
        axios(originalRequest)
          .then(res => resolve(res.data))
          .catch(err => {
            console.log('原请求环境异常', response ? JSON.stringify(response) : '响应失败');
            reject(err);
          });
      }).catch(err => {
        console.log('initialize请求环境异常', response ? JSON.stringify(response) : '响应失败');
        reject(err);
      });

      return;
    }

    if (response && (response.status === 200 || response.status === 304 || response.status === 400)) {
      resolve(response.data);
    } else if (response && (response.status === 403)) {
      console.log('403错误,当前环境缺少token', originalRequest && JSON.stringify(originalRequest.url));
      // 重新初始化并重试当前请求
      initialize("refresh").then(() => {
        // 重新请求原接口
        axios(originalRequest)
          .then(res => resolve(res.data))
          .catch(err => {
            console.log('原请求环境异常', response ? JSON.stringify(response) : '响应失败');
            reject(err);
          });
      }).catch(err => {
        console.log('initialize请求环境异常', response ? JSON.stringify(response) : '响应失败');
        reject(err);
      });
    } else {
      console.log('checkStatus请求环境异常', response ? JSON.stringify(response) : '响应失败');
      Toast.show({
        content: '网络异常，请检查网络连接是否正常',
      })
      reject(response);
    }
  });
}

const request = {
  post(url, params, config = {}) {
    const originalRequest = {
      method: "post",
      url,
      data: params,
      ...config
    };
    return axios(originalRequest).then(response => {
      return checkStatus(response, originalRequest)
    })
  },
  get(url, params, config = {}) {
    const originalRequest = {
      method: "get",
      url,
      params,
      ...config
    };
    return axios(originalRequest).then(response => {
      return checkStatus(response, originalRequest)
    })
  }
};
export default request;

// 显示加载动画
function showLoading(mode = 'full') {
  // 检查是否已经存在加载动画，避免重复创建
  if (document.getElementById('loading')) {
    return;
  }

  let dom = document.createElement('div');
  dom.setAttribute('id', 'loading');

  // 根据模式设置不同的class
  if (mode === 'icon') {
    dom.setAttribute('class', 'loading-icon-only');
  }

  document.body.appendChild(dom);

  const deviceType = getDeviceType();
  if (deviceType) {
    ReactDOM.render(<SpinLoading color='primary' />, dom);
    return;
  }
  ReactDOM.render(<DotLoading color='primary' />, dom);
}
// 隐藏加载动画
function hideLoading() {
  const loadingElement = document.getElementById('loading');
  if (loadingElement) {
    ReactDOM.unmountComponentAtNode(loadingElement); // 卸载 React 组件
    document.body.removeChild(loadingElement); // 移除 DOM 节点
  }
}