import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, But<PERSON>, List, message } from "antd";
import { ArrowLeftOutlined, RightOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import { PreloadImage } from "@/components/Image";
import folderIcon from "@/Resources/nasDiskImg/file-icon.png";
import { getPoolInfo, listDirectory } from "@/api/fatWall";
import CreateFolder from "../CreateFolder";
import { WebDavInfo, fetchWebDavInfo } from "@/utils/DeviceType";
import { downloadFromBaiduNetdisk, BaiduDownloadPathItem } from "@/api/nasDisk";
import EmptyState from "../EmptyState";
import createIcon from "@/Resources/nasDiskImg/create.png";
import createIconDark from "@/Resources/nasDiskImg/create-dark.png";
import { useTheme } from "@/utils/themeDetector";
import { useTaskNotification } from "@/layouts/Layout";
interface DownloadModalProps {
  visible: boolean;
  onClose: () => void;
  onDownload: (selectedPath: string, displayPath: string) => void;
  title?: string; // 添加自定义标题属性
  selectedFiles?: Array<{
    id: string;
    name: string;
    type: "folder" | "file";
    path: string;
  }>; // 选中的文件列表
  currentPath?: string; // 当前路径
}

interface FileItem {
  id: string;
  name: string;
  type: "folder" | "file";
  time: string;
  isLiked?: boolean;
  path: string;
  isDirectory?: boolean;
  dataDir?: string;
}

const DownloadModal: React.FC<DownloadModalProps> = ({
  visible,
  onClose,
  onDownload,
  title = "选择下载位置", // 默认标题
  selectedFiles = [], // 默认为空数组
  currentPath = "/",
}) => {
  const { isDarkMode } = useTheme();
  const { addNotification } = useTaskNotification();
  // 面包屑导航路径
  const [breadcrumbPath, setBreadcrumbPath] = useState<string[]>([
    "内部存储01",
  ]);

  // 当前路径 - 使用props中的currentPath作为初始值
  const [currentLocalPath, setCurrentLocalPath] = useState<string>(currentPath);

  // 保存根路径，用于重置和创建文件夹
  const rootPathRef = useRef<string>("");

  // 文件列表
  const [fileList, setFileList] = useState<FileItem[]>([]);

  // 选中的文件夹
  const [selectedFolder, setSelectedFolder] = useState<FileItem | null>(null);

  // 新建文件夹相关状态
  const [newFolderModalVisible, setNewFolderModalVisible] =
    useState<boolean>(false);

  // webDav配置
  const [webDavConfig, setWebDavConfig] = useState<WebDavInfo>();

  // 获取存储池信息
  const { run: fetchPoolInfo } = useRequest(getPoolInfo, {
    manual: true,
    onSuccess: (response) => {
      if (response.code === 0 && response.data) {
        // 获取WebDAV配置
        if (response.data.webDAV) setWebDavConfig(response.data.webDAV);

        // 获取第一个存储池的顶层目录作为顶层文件夹
        if (response.data.internal_pool.length > 0) {
          const firstPool = response.data.internal_pool[0];

          let pathParent = firstPool.data_dir;

          if (response.data.webDAV?.alias_root) {
            const dataDir = firstPool.data_dir.endsWith("/")
              ? firstPool.data_dir.slice(0, -1)
              : firstPool.data_dir;
            const aliasRoot = response.data.webDAV.alias_root;
            pathParent = aliasRoot + dataDir;
          }

          // 保存根路径，用于后续操作
          rootPathRef.current = pathParent;

          // 更新当前路径为正确的根路径
          setCurrentLocalPath(pathParent);

          // 获取顶层目录
          fetchDirectoryList({
            path: {
              parent: pathParent,
              recursion: false,
            },
          });
        }
      }
    },
    onError: (error) => {
      console.error("获取存储池信息失败：", error);
      setFileList([]);
    },
  });

  // 获取目录列表
  const { run: fetchDirectoryList, loading: directoryLoading } = useRequest(
    listDirectory,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          const files: FileItem[] = response.data.files
            .filter((file) => file.xattr.directory)
            .map((file, index) => ({
              id: `file_${index}`,
              name: file.name,
              type: "folder" as const,
              time: new Date(parseInt(file.modified_time))
                .toLocaleDateString("zh-CN", {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                  hour: "2-digit",
                  minute: "2-digit",
                })
                .replace(/\//g, "/")
                .replace(/,/g, ""),
              path: `${file.parent}/${file.name}`,
              isDirectory: file.xattr.directory,
              isLiked: file.xattr.favorite,
            }));
          setFileList(files);
        }
      },
      onError: (error) => {
        console.error("获取目录失败：", error);
        setFileList([]);
      },
    }
  );

  // 下载请求
  const { run: runDownload, loading: downloadLoading } = useRequest(
    (params: {
      remotePath: BaiduDownloadPathItem[];
      localPath: string;
      autotask: number;
    }) => {
      return downloadFromBaiduNetdisk({
        action: "download",
        autotask: params.autotask,
        remotepath: params.remotePath,
        localpath: params.localPath,
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (result.code === 0) {
          message.success("下载任务添加成功");
          onClose(); // 关闭弹窗
          // 添加任务通知
          addNotification();
        } else {
          message.error(`下载任务添加失败: ${result.result || "未知错误"}`);

          if (result.failed_paths && result.failed_paths.length > 0) {
            console.error("下载失败的路径:", result.failed_paths);
          }
        }
      },
      onError: (error) => {
        console.error("下载请求出错:", error);
        message.error("下载请求出错，请重试");
      },
    }
  );

  // 初始化时获取存储池信息和WebDAV配置
  useEffect(() => {
    if (visible) {
      fetchPoolInfo({});
      // 获取WebDAV配置信息
      fetchWebDavInfo()
        .then(() => {
          // 可以从utils中获取WebDAV配置
          import("@/utils/DeviceType").then(({ getWebDavInfo }) => {
            setWebDavConfig(getWebDavInfo());
          });
        })
        .catch((err) => {
          console.error("获取WebDAV配置失败:", err);
        });

      setSelectedFolder(null);
      setBreadcrumbPath(["内部存储01"]);
      // 不要在这里设置currentLocalPath为'/'，因为它会在fetchPoolInfo的回调中被更新
    }
  }, [visible, fetchPoolInfo]);

  // 处理文件夹点击
  const handleFolderClick = (folder: FileItem) => {
    // 导航到子文件夹
    setCurrentLocalPath(folder.path);
    setBreadcrumbPath([...breadcrumbPath, folder.name]);

    // 获取子文件夹内容
    fetchDirectoryList({
      path: {
        parent: folder.path,
        recursion: false,
      },
    });

    // 设置选中的文件夹
    setSelectedFolder(null);
  };

  // 处理文件夹选择
  const handleFolderSelect = (folder: FileItem) => {
    // 如果点击的是当前选中的文件夹，则取消选择
    if (selectedFolder?.id === folder.id) {
      setSelectedFolder(null);
    } else {
      // 否则选中该文件夹
      setSelectedFolder(folder);
    }
  };

  // 处理面包屑导航点击
  const handleBreadcrumbClick = (index: number) => {
    if (index === 0) {
      // 点击根目录
      setBreadcrumbPath(["内部存储01"]);

      // 使用保存的根路径
      const rootPath = rootPathRef.current || "/";
      setCurrentLocalPath(rootPath);

      // 获取根目录内容
      fetchDirectoryList({
        path: {
          parent: rootPath,
          recursion: false,
        },
      });

      setSelectedFolder(null);
    } else {
      // 点击中间路径
      const newPath = breadcrumbPath.slice(0, index + 1);
      setBreadcrumbPath(newPath);

      // 如果是第一级之后的路径，构建相对路径
      let pathToFetch;
      if (index === 1) {
        // 第二级目录，使用根路径
        pathToFetch = rootPathRef.current;
      } else {
        // 更深层级的目录，构建相对路径
        const relativePath = newPath.slice(2).join("/");
        pathToFetch = `${rootPathRef.current}/${relativePath}`;
      }

      setCurrentLocalPath(pathToFetch);

      // 获取该路径下的内容
      fetchDirectoryList({
        path: {
          parent: pathToFetch,
          recursion: false,
        },
      });

      // 更新选中的文件夹
      setSelectedFolder(null);
    }
  };

  // 处理确认下载
  const handleConfirm = () => {
    if (!selectedFolder) {
      message.warning("请选择一个文件夹");
      return;
    }

    // 获取显示路径
    const displayPath = breadcrumbPath.join(" > ");

    // 如果是自动下载场景（选择下载位置），直接调用onDownload回调
    if (title === "更改自动下载位置") {
      onDownload(selectedFolder.path, displayPath);
      onClose();
      return;
    }

    // 检查是否有选中的文件
    if (!selectedFiles || selectedFiles.length === 0) {
      message.warning("没有选择要下载的文件");
      return;
    }

    // 准备下载参数
    const remotePath: BaiduDownloadPathItem[] = selectedFiles.map((file) => ({
      type: file.type === "folder" ? "directory" : "file",
      path: file.path,
    }));
    // 调用下载接口
    runDownload({
      remotePath,
      localPath: selectedFolder.path,
      autotask: 0, // 非自动任务
    });

    // 调用下载回调（可能用于更新UI状态）
    onDownload(selectedFolder.path, displayPath);
  };

  // 打开新建文件夹弹窗
  const showNewFolderModal = () => {
    setNewFolderModalVisible(true);
  };

  // 关闭新建文件夹弹窗
  const closeNewFolderModal = () => {
    setNewFolderModalVisible(false);
  };

  // 创建文件夹成功后的回调
  const handleFolderCreated = () => {
    // 刷新当前目录，确保使用正确的路径
    fetchDirectoryList({
      path: {
        parent: currentLocalPath,
        recursion: false,
      },
    });
    closeNewFolderModal();

    // 创建成功后短暂延迟再刷新，确保列表完全更新
    setTimeout(() => {
      fetchDirectoryList({
        path: {
          parent: currentLocalPath,
          recursion: false,
        },
      });
    }, 500);
  };

  // 渲染面包屑导航
  const renderBreadcrumb = () => {
    return (
      <div className={styles.breadcrumbContainer}>
        {breadcrumbPath.map((item, index) => (
          <React.Fragment key={index}>
            {index > 0 && (
              <span className={styles.breadcrumbSeparator}>&gt;</span>
            )}
            <span
              className={`${styles.breadcrumbItem} ${
                index === breadcrumbPath.length - 1
                  ? styles.breadcrumbCurrent
                  : styles.breadcrumbLink
              }`}
              onClick={() => handleBreadcrumbClick(index)}
            >
              {item}
            </span>
          </React.Fragment>
        ))}
      </div>
    );
  };

  // 渲染新建文件夹按钮
  const renderNewFolderButton = () => {
    return (
      <div className={styles.newFolderButton} onClick={onClose}>
        <div className={styles.newFolderText}>取消</div>
      </div>
    );
  };

  return (
    <Modal
      title={
        <div className={styles.modalHeader}>
          <ArrowLeftOutlined className={styles.backIcon} onClick={onClose} />
          <span className={styles.modalTitle}>{title}</span>
          <PreloadImage
            src={isDarkMode ? createIconDark : createIcon}
            alt="新建文件夹"
            style={{width:40,height:40}}
            onClick={showNewFolderModal}
          />
        </div>
      }
      open={visible}
      footer={null}
      onCancel={onClose}
      width={546}
      className={styles.downloadModal}
      closeIcon={null}
      centered
      styles={{
        body: {
          height: "calc(636px - 95px)",
          padding: 0,
          overflow: "hidden",
        },
      }}
    >
      <div className={styles.modalContent}>
        <div className={styles.breadcrumbHeader}>{renderBreadcrumb()}</div>

        <div className={styles.fileListContainer}>
          {directoryLoading ? (
            <div className={styles.loadingContainer}>加载中...</div>
          ) : fileList.length > 0 ? (
            <List
              className={styles.fileList}
              dataSource={fileList}
              renderItem={(file) => (
                <List.Item
                  key={file.id}
                  className={`${styles.fileItem} ${
                    selectedFolder?.id === file.id ? styles.selectedItem : ""
                  }`}
                >
                  <div
                    className={`${styles.fileContent} ${
                      selectedFolder?.id === file.id
                        ? styles.selectedContent
                        : ""
                    }`}
                    onClick={() => handleFolderSelect(file)}
                    title="点击选择文件夹"
                  >
                    <PreloadImage
                      src={folderIcon}
                      alt="folder"
                      className={styles.fileIcon}
                    />
                    <div className={styles.fileInfo}>
                      <div className={styles.fileName}>{file.name}</div>
                      <div className={styles.fileDetails}>{file.time}</div>
                    </div>
                  </div>
                  <div
                    className={styles.arrowIcon}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFolderClick(file);
                    }}
                    title="进入文件夹"
                  >
                    <RightOutlined />
                  </div>
                </List.Item>
              )}
            />
          ) : (
            <div className={styles.emptyContainer}>
              <EmptyState />
            </div>
          )}
        </div>

        <div className={styles.footerContainer}>
          <div className={styles.footerLeft}>{renderNewFolderButton()}</div>
          <div className={styles.footerRight}>
            <Button
              type="primary"
              className={styles.confirmButton}
              disabled={!selectedFolder || downloadLoading}
              loading={downloadLoading}
              onClick={handleConfirm}
            >
              {downloadLoading ? "下载中..." : "确定"}
            </Button>
          </div>
        </div>
      </div>

      {/* 新建文件夹弹窗 */}
      <CreateFolder
        visible={newFolderModalVisible}
        onCancel={closeNewFolderModal}
        onSuccess={handleFolderCreated}
        currentPath={currentLocalPath}
        webDavConfig={webDavConfig}
      />
    </Modal>
  );
};

export default DownloadModal;
